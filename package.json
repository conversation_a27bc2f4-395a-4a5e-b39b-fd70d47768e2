{"name": "personal-coach", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install", "format": "prettier --write ."}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "next": "15.3.5", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "swiper": "^11.2.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.30.1", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^10.1.5", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "typescript": "^5"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}