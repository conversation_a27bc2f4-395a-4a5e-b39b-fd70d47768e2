import Image from 'next/image';
import React from 'react';

interface MissionCardProps {
  title: string;
  description: string;
  titleColor?: string;
}

const MissionCard: React.FC<MissionCardProps> = ({ title, description, titleColor = 'text-red-500' }) => {
  return (
    <div className="flex flex-col items-center text-center p-6  rounded-xl border border-neutral-200 dark:border-none  shadow-sm hover:shadow-md transition-shadow duration-300">
      <h3 className={`text-lg font-semibold mb-3 ${titleColor}`}>
        {title}
      </h3>
      <p className="text-neutral-600 dark:text-neutral-300 text-sm leading-relaxed">
        {description}
      </p>
    </div>
  );
};

const Mission: React.FC = () => {
  const missionCards = [
    {
      title: '100 Million Lives',
      description: 'Touched and transformed by 2030',
      titleColor: 'text-red-500'
    },
    {
      title: 'Influential Leaders',
      description: 'Creating a generation of conscious leaders',
      titleColor: 'text-red-500'
    },
    {
      title: 'Better World',
      description: 'Contributing to global transformation',
      titleColor: 'text-red-500'
    }
  ];

  return (
    <section className="w-full max-w-6xl mx-auto px-4 py-12">
      <div className="text-center mb-12">
        <div className="mb-ml bg-primary-10 w-4-xl h-4-xl mx-auto flex items-center justify-center rounded-full">
                     <Image
                       src="assets/svgs/global.svg"
                       alt="our mission"
                       width={28}
                       height={28}
                       className="h-7 w-7"
                       style={{
                         filter:
                           'invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%)',
                       }}
                     />{' '}
                   </div>
        <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-50 mb-2">
          Our Mission
        </h2>
      </div>

      {/* Mission Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {missionCards.map((card, index) => (
          <MissionCard
            key={index}
            title={card.title}
            description={card.description}
            titleColor={card.titleColor}
          />
        ))}
      </div>
    </section>
  );
};

export default Mission;
