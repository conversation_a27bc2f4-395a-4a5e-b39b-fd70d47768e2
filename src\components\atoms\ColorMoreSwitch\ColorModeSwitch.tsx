'use client';

import React, { useEffect, useState } from 'react';
import { useTheme } from 'next-themes';
import Image from 'next/image';

const ColorModeSwitch = () => {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div>
      <div>
        {theme === 'dark' ? (
          <button onClick={() => setTheme('light')}>
            <Image
              height={32}
              width={32}
              color="white"
              src="/assets/svgs/sun.svg"
              alt="sun icon"
            />
          </button>
        ) : (
          <button onClick={() => setTheme('dark')}>
            <Image
              height={32}
              width={32}
              color="white"
              src="/assets/svgs/moon.svg"
              alt="moon icon"
            />
          </button>
        )}
      </div>
    </div>
  );
};

export default ColorModeSwitch;
