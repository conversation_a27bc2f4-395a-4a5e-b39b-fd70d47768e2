// src/store/testSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface YourState {
  value: number;
}

const initialState: YourState = {
  value: 0,
};

const testSlice = createSlice({
  name: 'testSlice',
  initialState,
  reducers: {
    increment(state) {
      state.value += 1;
    },
    decrement(state) {
      state.value -= 1;
    },
    setValue(state, action: PayloadAction<number>) {
      state.value = action.payload;
    },
  },
});

export const { increment, decrement, setValue } = testSlice.actions;

export default testSlice.reducer;
