'use client';

// import { Swiper, SwiperSlide } from 'swiper/react';
// import { Navigation, EffectCoverflow, Autoplay } from 'swiper/modules';
// import 'swiper/css';
// import 'swiper/css/navigation';
// import 'swiper/css/effect-coverflow';
// import Image from 'next/image';

// const images = [
//   { src: '/assets/about/carousel-1.webp', width: 400, height: 200 },
//   { src: '/assets/about/carousel-2.webp', width: 400, height: 200 },
//   { src: '/assets/about/carousel-3.webp', width: 400, height: 200 },
//   { src: '/assets/about/carousel-4.webp', width: 400, height: 200 },
//   { src: '/assets/about/carousel-5.webp', width: 400, height: 200 },
// ];

// const ImageCarousel = () => {
//   return (
//     <div style={{ maxWidth: '100%', overflow: 'hidden', padding: '0 20px' }}>
//       <Swiper
//         modules={[Navigation, EffectCoverflow, Autoplay]}
//         effect={'coverflow'}
//         grabCursor={true}
//         centeredSlides={true}
//         slidesPerView={3} // Ensures 3 slides are visible at once
//         spaceBetween={5} // Further reduced gap to 5px
//         loop={true} // Infinite looping
//         coverflowEffect={{
//           rotate: 0,
//           stretch: 0,
//           depth: 200, // Increased depth for a more pronounced 3D effect
//           modifier: 1, // Adjusted for smoother transitions
//           slideShadows: false, // Disabled shadows for simplicity
//         }}
//         navigation
//         autoplay={{ delay: 3000, disableOnInteraction: false }}
//         style={{ width: '100%', maxWidth: '1200px', margin: '0 auto' }}
//       >
//         {images.map((image, index) => (
//           <SwiperSlide
//             key={index}
//             style={{
//               display: 'flex',
//               justifyContent: 'center',
//               alignItems: 'center',
//               width: 'auto', // Let Swiper handle width dynamically
//               maxWidth: '400px', // Cap slide width to match image
//             }}
//           >
//             <Image
//               src={image.src}
//               alt={`Carousel Image ${index + 1}`}
//               width={image.width}
//               height={image.height}
//               style={{
//                 width: '100%',
//                 height: 'auto',
//                 objectFit: 'cover', // Changed to 'cover' for better fit
//               }}
//             />
//           </SwiperSlide>
//         ))}
//       </Swiper>
//     </div>
//   );
// };

// export default ImageCarousel;
import Image from "next/image";
import React, { useEffect, useState } from "react";


const images = [
  '/assets/about/carousel-1.webp',
  '/assets/about/carousel-2.webp',
  '/assets/about/carousel-3.webp',
  '/assets/about/carousel-4.webp',
  '/assets/about/carousel-5.webp',
];

const ImageCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const updateCarousel = (index:number) => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((index + images.length) % images.length);
    setTimeout(() => setIsAnimating(false), 800);
  };

  const getCardClass = (index:number) => {
    const diff = (index - currentIndex + images.length) % images.length;

    // Center image - largest
    if (diff === 0) return "z-10 scale-110";
    // Adjacent images - medium size
    if (diff === 1) return "z-5 translate-x-20 sm:translate-x-28 md:translate-x-32 lg:translate-x-56 scale-90";
    if (diff === images.length - 1) return "z-5 -translate-x-20 sm:-translate-x-28 md:-translate-x-32 lg:-translate-x-56 scale-90";
    // Outer images - smallest size
    if (diff === 2) return "z-1 translate-x-30 sm:translate-x-56 md:translate-x-60 lg:translate-x-96 scale-75";
    if (diff === images.length - 2) return "z-1 -translate-x-30 sm:-translate-x-56 md:-translate-x-60 lg:-translate-x-96 scale-75";

    return "opacity-0 pointer-events-none";
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === "ArrowLeft") updateCarousel(currentIndex - 1);
    if (e.key === "ArrowRight") updateCarousel(currentIndex + 1);
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  });

  return (
    <div className="w-full max-w-[1100px] mx-auto mt-10 px-4">
      <div className="mb-8 text-center">
        <h2 className="mb-2 text-2xl font-bold sm:text-3xl md:text-4xl text-primary">image title</h2>
        <p className="mx-auto max-w-2xl text-base text-black sm:text-lg md:text-xl">
        Image Carousel
        </p>
      </div>
      <div className="h-[240px] sm:h-[340px] md:h-[450px] relative overflow-visible flex items-center justify-center">
        <button
          onClick={() => updateCarousel(currentIndex - 1)}
          className="hidden absolute -left-6 top-1/2 z-20 justify-center items-center w-8 h-8 rounded-full transform -translate-y-1/2 cursor-pointer md:flex sm:-left-10 md:-left-8 sm:w-10 sm:h-10 bg-main hover:bg-primary"
          aria-label="Previous"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 6L9 12L15 18" stroke="white" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </button>

        <div className="w-full h-full flex justify-center items-center relative transition-transform duration-800 ease-[cubic-bezier(0.25,0.46,0.45,0.94)] perspective-[1000px]">
          {images.map((src, index) => {
            return (
              <div
                key={index}
                className={`absolute w-[135px] h-[170px] sm:w-[220px] sm:h-[260px] md:w-[250px] md:h-[350px] lg:w-[300px] lg:h-[400px]  rounded-2xl shadow-xl overflow-hidden transition-all duration-800 ease-[cubic-bezier(0.25,0.46,0.45,0.94)] cursor-pointer transform ${getCardClass(index)}`}
                onClick={() => updateCarousel(index)}
              >
                <Image
                width={300}
                height={300}
                  src={src}
                  alt={`Slide ${index}`}
                  className="object-cover w-full h-full transition-all duration-700"
                />
              </div>
            );
          })}
        </div>

        <button
          onClick={() => updateCarousel(currentIndex + 1)}
          className="hidden absolute -right-6 top-1/2 z-20 justify-center items-center w-8 h-8 rounded-full transform -translate-y-1/2 cursor-pointer md:flex sm:-right-10 md:-right-8 sm:w-10 sm:h-10 bg-main hover:bg-primary"
          aria-label="Next"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 6L15 12L9 18" stroke="white" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </button>
      </div>
      {/* Dots below carousel */}
      <div className="hidden gap-2 justify-center mt-8 md:flex">
        {images.map((_, i) => (
          <div
            key={i}
            onClick={() => updateCarousel(i)}
            className={`w-3 h-3 rounded-full transition-all duration-300 cursor-pointer ${
              i === currentIndex ? "bg-primary scale-110" : "bg-main"
            }`}
          ></div>
        ))}
      </div>
    </div>
  );
};

export default ImageCarousel;