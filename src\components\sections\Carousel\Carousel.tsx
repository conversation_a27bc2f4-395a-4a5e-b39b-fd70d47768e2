'use client';

// import { Swiper, SwiperSlide } from 'swiper/react';
// import { Navigation, EffectCoverflow, Autoplay } from 'swiper/modules';
// import 'swiper/css';
// import 'swiper/css/navigation';
// import 'swiper/css/effect-coverflow';
// import Image from 'next/image';

// const images = [
//   { src: '/assets/about/carousel-1.webp', width: 400, height: 200 },
//   { src: '/assets/about/carousel-2.webp', width: 400, height: 200 },
//   { src: '/assets/about/carousel-3.webp', width: 400, height: 200 },
//   { src: '/assets/about/carousel-4.webp', width: 400, height: 200 },
//   { src: '/assets/about/carousel-5.webp', width: 400, height: 200 },
// ];

// const ImageCarousel = () => {
//   return (
//     <div style={{ maxWidth: '100%', overflow: 'hidden', padding: '0 20px' }}>
//       <Swiper
//         modules={[Navigation, EffectCoverflow, Autoplay]}
//         effect={'coverflow'}
//         grabCursor={true}
//         centeredSlides={true}
//         slidesPerView={3} // Ensures 3 slides are visible at once
//         spaceBetween={5} // Further reduced gap to 5px
//         loop={true} // Infinite looping
//         coverflowEffect={{
//           rotate: 0,
//           stretch: 0,
//           depth: 200, // Increased depth for a more pronounced 3D effect
//           modifier: 1, // Adjusted for smoother transitions
//           slideShadows: false, // Disabled shadows for simplicity
//         }}
//         navigation
//         autoplay={{ delay: 3000, disableOnInteraction: false }}
//         style={{ width: '100%', maxWidth: '1200px', margin: '0 auto' }}
//       >
//         {images.map((image, index) => (
//           <SwiperSlide
//             key={index}
//             style={{
//               display: 'flex',
//               justifyContent: 'center',
//               alignItems: 'center',
//               width: 'auto', // Let Swiper handle width dynamically
//               maxWidth: '400px', // Cap slide width to match image
//             }}
//           >
//             <Image
//               src={image.src}
//               alt={`Carousel Image ${index + 1}`}
//               width={image.width}
//               height={image.height}
//               style={{
//                 width: '100%',
//                 height: 'auto',
//                 objectFit: 'cover', // Changed to 'cover' for better fit
//               }}
//             />
//           </SwiperSlide>
//         ))}
//       </Swiper>
//     </div>
//   );
// };

// export default ImageCarousel;
import Image from "next/image";
import React, { useEffect, useState } from "react";


const images = [
  '/assets/about/carousel-1.webp',
  '/assets/about/carousel-2.webp',
  '/assets/about/carousel-3.webp',
  '/assets/about/carousel-4.webp',
  '/assets/about/carousel-5.webp',
];

const ImageCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const updateCarousel = (index:number) => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((index + images.length) % images.length);
    setTimeout(() => setIsAnimating(false), 800);
  };

  const getCardClass = (index:number) => {
    const diff = (index - currentIndex + images.length) % images.length;

    // Center image - largest and most prominent
    if (diff === 0) return "z-20 scale-110 w-[220px] sm:w-[280px] md:w-[340px] lg:w-[400px]";

    // Right side images
    if (diff === 1) return "z-10 translate-x-[180px] sm:translate-x-[230px] md:translate-x-[280px] lg:translate-x-[330px] scale-75 opacity-80";
    if (diff === 2) return "z-5 translate-x-[320px] sm:translate-x-[390px] md:translate-x-[460px] lg:translate-x-[530px] scale-60 opacity-60";

    // Left side images (mirror of right side)
    if (diff === images.length - 1) return "z-10 -translate-x-[180px] sm:-translate-x-[230px] md:-translate-x-[280px] lg:-translate-x-[330px] scale-75 opacity-80";
    if (diff === images.length - 2) return "z-5 -translate-x-[320px] sm:-translate-x-[390px] md:-translate-x-[460px] lg:-translate-x-[530px] scale-60 opacity-60";

    return "opacity-0 pointer-events-none scale-50";
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === "ArrowLeft") updateCarousel(currentIndex - 1);
    if (e.key === "ArrowRight") updateCarousel(currentIndex + 1);
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  });

  return (
    <div className="w-full max-w-[1100px] mx-auto mt-10 px-4">
      <div className="mb-8 text-center">
        <h2 className="mb-2 text-2xl font-bold sm:text-3xl md:text-4xl dark:text-neutral-50 text-neutral-1000">A Journey of Real Impact</h2>
        <p className="mx-auto max-w-2xl text-base text-neutral-600 dark:text-neutral-300 sm:text-lg md:text-xl">
        Moments that shaped our mission — captured from real coaching sessions, workshops, and community growth.
        </p>
      </div>
      <div className="h-[280px] sm:h-[360px] md:h-[420px] lg:h-[480px] relative overflow-hidden flex items-center justify-center">
        <button
          onClick={() => updateCarousel(currentIndex - 1)}
          className="absolute left-4 top-1/2 z-30 flex justify-center items-center w-10 h-10 rounded-full transform -translate-y-1/2 cursor-pointer bg-black/20 hover:bg-black/40 backdrop-blur-sm transition-all duration-300"
          aria-label="Previous"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 6L9 12L15 18" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </button>

        <div className="w-full h-full flex justify-center items-center relative">
          {images.map((src, index) => {
            return (
              <div
                key={index}
                className={`absolute w-[180px] h-[240px] sm:w-[220px] sm:h-[280px] md:w-[260px] md:h-[320px] lg:w-[300px] lg:h-[360px] rounded-2xl overflow-hidden transition-all duration-700 ease-out cursor-pointer ${getCardClass(index)}`}
                onClick={() => updateCarousel(index)}
              >
                <Image
                  width={400}
                  height={360}
                  src={src}
                  alt={`Slide ${index}`}
                  className="object-cover w-full h-full rounded-2xl"
                />
              </div>
            );
          })}
        </div>

        <button
          onClick={() => updateCarousel(currentIndex + 1)}
          className="absolute right-4 top-1/2 z-30 flex justify-center items-center w-10 h-10 rounded-full transform -translate-y-1/2 cursor-pointer bg-black/20 hover:bg-black/40 backdrop-blur-sm transition-all duration-300"
          aria-label="Next"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 6L15 12L9 18" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </button>
      </div>
      {/* Dots below carousel */}
      <div className="hidden gap-2 justify-center mt-8 md:flex">
        {images.map((_, i) => (
          <div
            key={i}
            onClick={() => updateCarousel(i)}
            className={`w-3 h-3 rounded-full transition-all duration-300 cursor-pointer ${
              i === currentIndex ? "bg-primary scale-110" : "bg-main"
            }`}
          ></div>
        ))}
      </div>
    </div>
  );
};

export default ImageCarousel;