import React from 'react';
import Image from 'next/image';

const HeroSection = () => {
  return (
    <div className="gap-5-xl md:gap-2-xl mt-10 flex flex-col items-center justify-between md:flex-row">
      {/* Text Content */}
      <div className="space-y-ml flex-1 text-center md:text-left">
        <p className="text-primary-400 flex items-center justify-center text-sm font-normal tracking-wide md:justify-start">
          <span className="bg-primary-400 mr-1 inline-block h-0.5 w-[40px]"></span>
          Engineer to Entrepreneur to Coach
        </p>

        <h1 className="text-3xl font-bold text-neutral-900 md:text-4xl lg:text-5xl dark:text-neutral-100">
          It&apos;s More Than Coaching It&apos;s Your 
          <span className="text-primary-300">Breakthrough</span>
        </h1>
        <p className="text-md leading-relaxed text-neutral-600 dark:text-neutral-300">
          From Engineer to Coach, Transforming Pain into Purpose
        </p>
      </div>

      {/* Image */}
      <div className="relative flex-1 before:absolute before:top-0 before:left-0 before:z-0 before:h-[100px] before:w-[100px] before:-translate-x-1/4 before:-translate-y-1/4 before:rounded-full before:border before:border-red-600 before:content-[''] after:absolute after:right-0 after:bottom-0 after:z-0 after:h-[100px] after:w-[100px] after:translate-x-1/4 after:translate-y-1/4 after:rounded-full after:border after:border-red-600 after:content-[''] md:before:h-[120px] md:before:w-[120px] md:after:h-[120px] md:after:w-[120px] lg:before:h-[150px] lg:before:w-[150px] lg:after:h-[150px] lg:after:w-[150px]">
        <Image
          src="/assets/about/about.webp"
          alt="Hero Image"
          width={590}
          height={378}
          className="rounded-6 relative z-1 h-auto w-full object-cover"
          priority
        />
      </div>
    </div>
  );
};

export default HeroSection;
