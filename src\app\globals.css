@import 'tailwindcss';
@import 'swiper/css';
@import 'swiper/css/effect-coverflow';
@import 'swiper/css/pagination';

@variant dark (html.dark &);

@theme {
  /* Colors */
  --color-primary-100: #fac1c1;
  --color-primary-200: #f68484;
  --color-primary-300: #f14646;
  --color-primary-400: #ee1a1a;
  --color-primary-500: #c90f0f;
  --color-primary-600: #9c0c0c;
  --color-primary-700: #700808;
  --color-primary-10: #f146461a;
  --color-primary-20: #f1464633;
  --color-primary-30: #f146464d;
  --color-primary-40: #f1464666;
  --color-primary-50: #f1464680;

  --color-secondary-100: #e9eaec;
  --color-secondary-200: #d2d5da;
  --color-secondary-300: #bcc0c7;
  --color-secondary-400: #a6abb5;
  --color-secondary-500: #9097a2;
  --color-secondary-600: #7a8290;
  --color-secondary-700: #676e7b;
  --color-secondary-800: #555b65;
  --color-secondary-900: #424750;
  --color-secondary-1000: #30343a;
  --color-secondary-10: #bcc0c71a;
  --color-secondary-20: #bcc0c733;
  --color-secondary-30: #bcc0c74d;
  --color-secondary-40: #bcc0c766;
  --color-secondary-50: #bcc0c780;

  --color-neutral-100: #ffffff;
  --color-neutral-200: #e3e3e3;
  --color-neutral-300: #c6c6c6;
  --color-neutral-400: #aaaaaa;
  --color-neutral-500: #8e8e8e;
  --color-neutral-600: #717171;
  --color-neutral-700: #555555;
  --color-neutral-800: #393939;
  --color-neutral-900: #1c1c1c;
  --color-neutral-1000: #000000;
  --color-neutral-10: #0000001a;

  --color-red-100: #fb3748;
  --color-red-200: #d00416;
  --color-red-10: #fb37481a;

  --color-yellow-100: #ffdb43;
  --color-yellow-200: #dfb400;
  --color-yellow-10: #ffdb431a;

  --color-green-100: #84ebb4;
  --color-green-200: #1fc16b;
  --color-green-10: #1fc16b1a;

  /* Spacing */
  --spacing-xs: 2px;
  --spacing-s: 4px;
  --spacing-sm: 8px;
  --spacing-m: 12px;
  --spacing-ml: 16px;
  --spacing-l: 20px;
  --spacing-xl: 24px;
  --spacing-2-xl: 32px;
  --spacing-3-xl: 40px;
  --spacing-4-xl: 48px;
  --spacing-5-xl: 56px;

  /* Border Radius */
  --radius-0: 0px;
  --radius-1: 4px;
  --radius-2: 8px;
  --radius-3: 12px;
  --radius-4: 16px;
  --radius-5: 20px;
  --radius-6: 24px;
  --radius-7: 32px;
  --radius-8: 999px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  min-height: 100dvh;
}
