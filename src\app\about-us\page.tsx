import ImageCarousel from '@/components/sections/Carousel/Carousel';
import HeroSection from '@/components/sections/HeroSection/HeroSection';
import IconCards from '@/components/sections/IconCards/IconCards';
import React from 'react';

const AboutUs = () => {
  const storyCards = [
    {
      icon: '/assets/svgs/heart.svg',
      title: 'Early Struggles',
      description:
        "Childhood marked by family trauma - father's imprisonment, grandfather's death, and personal battles with addiction and self-confidence.",
    },
    {
      icon: '/assets/svgs/flash.svg',
      title: 'Engineering Foundation',
      description:
        'Built a successful career as an electrical engineer, developing analytical thinking and problem-solving skills that would later serve my coaching practice.',
    },
    {
      icon: '/assets/svgs/status-up.svg',
      title: 'Entrepreneurial Success',
      description:
        'Grew a startup from zero to $2.5M in revenue, leading and managing a team of 20 employees through rapid expansion and market challenges. ',
    },
    {
      icon: '/assets/svgs/profile-2user.svg',
      title: 'Transformation & Calling',
      description:
        'Discovered personal development through training with <PERSON> and <PERSON><PERSON> <PERSON><PERSON><PERSON>. Realized my true calling was helping others break through their limitations.',
    },
  ];

  return (
    <main className="mx-18 flex flex-col gap-10">
      <HeroSection />
      <IconCards
        mainText="The Story Behind the Coach"
        mainDescription="My journey from engineering to entrepreneurship to coaching wasn't planned—it was forged through struggle, breakthrough, and the deep desire to help others avoid the pain I experienced while finding their own path to purpose."
        cards={storyCards}
        columns={4}
      />
      <div className="py-4-xl gap-4-xl rounded-6 my-3 flex flex-col px-6 text-center shadow-2xl md:gap-2 dark:shadow-none">
        <p className="text-md leading-relaxed text-neutral-600 dark:text-neutral-300">
          Today, I combine my engineering mindset with deep emotional
          intelligence, business acumen, and world-class coaching training. I
          specialize in helping high-achievers break through the invisible
          barriers that keep them stuck, whether in business, relationships, or
          personal fulfillment.
        </p>
        <p className="text-md leading-relaxed text-neutral-600 dark:text-neutral-300">
          As a corporate leadership trainer, NLP practitioner, and entrepreneur,
          I bring a unique perspective that bridges analytical thinking with
          transformational coaching. My mission is clear: to touch 100 million
          lives by 2030 and build an influential generation that makes the world
          better.
        </p>
      </div>
      <ImageCarousel />
    </main>
  );
};

export default AboutUs;
