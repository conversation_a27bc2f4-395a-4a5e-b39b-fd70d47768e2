import SectionHeading from '@/components/molecules/SectionHeading';
import ImageCarousel from '@/components/sections/Carousel/Carousel';
import HeroSection from '@/components/sections/HeroSection/HeroSection';
import IconCards from '@/components/blocks/IconCards/IconCards';
import Mission from '@/components/sections/Mission/Mission';
import React from 'react';

const AboutUs = () => {
  const storyCards = [
    {
      icon: '/assets/svgs/heart.svg',
      title: 'Early Struggles',
      description:
        "Childhood marked by family trauma - father's imprisonment, grandfather's death, and personal battles with addiction and self-confidence.",
    },
    {
      icon: '/assets/svgs/flash.svg',
      title: 'Engineering Foundation',
      description:
        'Built a successful career as an electrical engineer, developing analytical thinking and problem-solving skills that would later serve my coaching practice.',
    },
    {
      icon: '/assets/svgs/status-up.svg',
      title: 'Entrepreneurial Success',
      description:
        'Grew a startup from zero to $2.5M in revenue, leading and managing a team of 20 employees through rapid expansion and market challenges. ',
    },
    {
      icon: '/assets/svgs/profile-2user.svg',
      title: 'Transformation & Calling',
      description:
        'Discovered personal development through training with <PERSON> and <PERSON><PERSON> <PERSON><PERSON>. Realized my true calling was helping others break through their limitations.',
    },
  ];
  const corePhilosophyCards = [
    {
      icon: '/assets/svgs/heart.svg',
      title: 'Clarity blooms from struggle',
      description:
"Your greatest challenges contain the seeds of your greatest breakthroughs. I help you transform pain into purpose and obstacles into opportunities. "    },
    {
      icon: '/assets/svgs/goals.svg',
      title: 'Action creates momentum',
      description:
"Transformation requires courage to face the truth about where you are and where you want to go. We'll have the conversations that matter most."    },
    {
      icon: '/assets/svgs/mind.svg',
      title: 'Growth requires courage',
      description:
"Purpose isn't something you discover lying around—it's something you create through aligned action, clear values, and consistent commitment to growth. "    },
  ];
  const BussinessCards = [
    {
      icon: '/assets/svgs/profile-2user.svg',
      title: 'Employees Led',
      iconText:'20',
      description:
" Through rapid business growth"    },
    {
      icon: '/assets/svgs/dollar-circle.svg',
      title: 'Business Built',
       iconText:'$2.5M',
      description:
"From startup to success"    },
    {
      icon: '/assets/svgs/monitor.svg',
      title: 'Clients & Workshops',
       iconText:'300+',
      description:
"Transformed through coaching"    },
  ];

  return (
    <main className="mx-18 flex flex-col gap-10">
      <HeroSection />
      <IconCards
        cards={storyCards}
        columns={4}
      />
      <div className="py-4-xl gap-4-xl rounded-6 my-3 flex flex-col px-6 text-center shadow-2xl md:gap-2 dark:shadow-none">
        <p className="text-md leading-relaxed text-neutral-600 dark:text-neutral-300">
          Today, I combine my engineering mindset with deep emotional
          intelligence, business acumen, and world-class coaching training. I
          specialize in helping high-achievers break through the invisible
          barriers that keep them stuck, whether in business, relationships, or
          personal fulfillment.
        </p>
        <p className="text-md leading-relaxed text-neutral-600 dark:text-neutral-300">
          As a corporate leadership trainer, NLP practitioner, and entrepreneur,
          I bring a unique perspective that bridges analytical thinking with
          transformational coaching. My mission is clear: to touch 100 million
          lives by 2030 and build an influential generation that makes the world
          better.
        </p>
      </div>
      <ImageCarousel />
      <div>
      <SectionHeading headline='Core Philosophy' subLine='These beliefs, forged through my own transformation from struggle to success, guide every coaching conversation and shape my approach to breakthrough work.' />
        <IconCards
        cards={corePhilosophyCards}
        columns={3}
      />
      </div>
     
      <div>
      <SectionHeading headline='Core Philosophy' subLine='These beliefs, forged through my own transformation from struggle to success, guide every coaching conversation and shape my approach to breakthrough work.' />
        <IconCards
        cards={BussinessCards}
        columns={3}
      />
      </div>

      <Mission />

    </main>
  );
};

export default AboutUs;
