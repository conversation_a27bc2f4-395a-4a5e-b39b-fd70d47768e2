import type { Metadata } from 'next';
import { Poppins } from 'next/font/google';
import './globals.css';
import ReduxProvider from '@/components/ReduxProvider/ReduxProvider';
import { ThemeProvider } from 'next-themes';
import ColorModeSwitch from '@/components/atoms/ColorMoreSwitch/ColorModeSwitch';

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-poppins',
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${poppins.variable} ${poppins.variable} antialiased`}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <ReduxProvider>
            <nav className="nav-menu">
              {' '}
              <ColorModeSwitch />
            </nav>
            <div className="main-content">{children}</div>
            <footer className="footer">{/* footer will be here */}</footer>
          </ReduxProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
