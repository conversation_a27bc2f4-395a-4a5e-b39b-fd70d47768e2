import React from 'react';
import Image from 'next/image';

interface Card {
  icon: string;
  title: string;
  description: string;
  iconText?: string;
}

interface IconCardsProps {

  cards: Card[];
  columns?: 2 | 3 | 4;
  className?: string;
}

const IconCards: React.FC<IconCardsProps> = ({
  cards,
  columns = 4,
  className = '',
}) => {
  const gridCols = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  return (
    <div className={`pb-5-xl ${className}`}>
     

      {/* Cards Grid */}
      <div className={`grid ${gridCols[columns]} gap-xl`}>
        {cards.map((card, index) => (
          <div
            key={index}
            className="p-xl rounded-3 text-center shadow-xl dark:border dark:border-neutral-900 dark:shadow-none"
          >
            <div className="mb-ml bg-primary-10 w-4-xl h-4-xl mx-auto flex items-center justify-center rounded-full">
              <Image
                src={card.icon}
                alt={card.title}
                width={28}
                height={28}
                className="h-7 w-7"
                style={{
                  filter:
                    'invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%)',
                }}
              />{' '}
            </div>
            {card.iconText && (
              <p className="text-primary-400 mb-sm text-lg font-semibold ">
                {card.iconText}
              </p>
            )}
            <h3 className="text-neutral-1000 mb-sm text-lg font-semibold dark:text-neutral-100">
              {card.title}
            </h3>
            <p className="text-sm leading-relaxed text-neutral-500 dark:text-neutral-300">
              {card.description}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default IconCards;
